import React from 'react';

const RoonyIcon: React.FC = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        {/* Tête du robot Roony */}
        <rect x="6" y="4" width="12" height="10" rx="2" strokeWidth={2} className="fill-indigo-500/20"/>
        {/* Yeux */}
        <circle cx="9" cy="8" r="1.5" className="fill-indigo-400"/>
        <circle cx="15" cy="8" r="1.5" className="fill-indigo-400"/>
        {/* Bouche souriante */}
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 11c1 1 3 1 4 0" />
        {/* Antenne */}
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4V2" />
        <circle cx="12" cy="2" r="1" className="fill-indigo-400"/>
        {/* Corps */}
        <rect x="8" y="14" width="8" height="6" rx="1" strokeWidth={2} className="fill-indigo-500/10"/>
        {/* Bras */}
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 16h2M16 16h2" />
    </svg>
);


export const Header: React.FC = () => {
  return (
    <header className="bg-slate-900/60 backdrop-blur-sm border-b border-slate-800 sticky top-0 z-10">
      <div className="container mx-auto px-4 py-3 flex items-center">
        <RoonyIcon />
        <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-indigo-500">
          Roony - Votre Partenaire IA
        </h1>
      </div>
    </header>
  );
};